<?php

namespace Tests\Feature;

use App\Enums\Trips\TripStatus;
use App\Models\Trip;
use App\Models\TripCancellation;
use App\Models\User;
use App\Models\Rider;
use App\Models\Driver;
use App\Traits\HasTripStatusColumn;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TripStatusIconConsistencyTest extends TestCase
{
    use RefreshDatabase;

    private $testClass;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an anonymous class that uses the trait for testing
        $this->testClass = new class {
            use HasTripStatusColumn;
        };
    }

    /** @test */
    public function no_show_cancellation_uses_consistent_icon_regardless_of_status()
    {
        // Create a rider and driver
        $riderUser = User::factory()->create();
        $rider = Rider::factory()->create(['user_id' => $riderUser->id]);
        
        $driverUser = User::factory()->create();
        $driver = Driver::factory()->create(['user_id' => $driverUser->id]);

        // Test Case 1: Trip with status 'canceled' but cancellation reason 'no_show'
        $trip1 = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::canceled,
        ]);

        $cancellation1 = TripCancellation::create([
            'trip_id' => $trip1->id,
            'user_id' => $driverUser->id,
            'cancelled_by' => 'driver',
            'reason' => 'no_show',
            'cancelled_at' => now(),
        ]);

        // Test Case 2: Trip with status 'no_show' and cancellation reason 'no_show'
        $trip2 = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::no_show,
        ]);

        $cancellation2 = TripCancellation::create([
            'trip_id' => $trip2->id,
            'user_id' => $driverUser->id,
            'cancelled_by' => 'driver',
            'reason' => 'no_show',
            'cancelled_at' => now(),
        ]);

        // Load the cancellation relationships
        $trip1->load('cancellation');
        $trip2->load('cancellation');

        // Get the icon closures
        $iconClosure = $this->testClass::getTripStatusIcon();
        $colorClosure = $this->testClass::getTripStatusColor();
        $stateClosure = $this->testClass::getTripStatusState();

        // Test that both trips return the same icon (no_show icon)
        $icon1 = $iconClosure($trip1);
        $icon2 = $iconClosure($trip2);
        
        $this->assertEquals(TripStatus::no_show->getIcon(), $icon1);
        $this->assertEquals(TripStatus::no_show->getIcon(), $icon2);
        $this->assertEquals($icon1, $icon2, 'Both no_show cancellations should use the same icon');

        // Test that both trips return the same color (no_show color)
        $color1 = $colorClosure($trip1);
        $color2 = $colorClosure($trip2);
        
        $this->assertEquals(TripStatus::no_show->getColor(), $color1);
        $this->assertEquals(TripStatus::no_show->getColor(), $color2);
        $this->assertEquals($color1, $color2, 'Both no_show cancellations should use the same color');

        // Test that both trips return the same state text
        $state1 = $stateClosure($trip1);
        $state2 = $stateClosure($trip2);
        
        $this->assertEquals('No Show', $state1);
        $this->assertEquals('No Show', $state2);
        $this->assertEquals($state1, $state2, 'Both no_show cancellations should show the same text');
    }

    /** @test */
    public function regular_canceled_trip_uses_canceled_icon()
    {
        // Create a rider and driver
        $riderUser = User::factory()->create();
        $rider = Rider::factory()->create(['user_id' => $riderUser->id]);
        
        $driverUser = User::factory()->create();
        $driver = Driver::factory()->create(['user_id' => $driverUser->id]);

        // Create a regular canceled trip (not no_show)
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::canceled,
            'cancelled_by' => 'driver',
        ]);

        $cancellation = TripCancellation::create([
            'trip_id' => $trip->id,
            'user_id' => $driverUser->id,
            'cancelled_by' => 'driver',
            'reason' => 'driver_delay', // Not no_show
            'cancelled_at' => now(),
        ]);

        $trip->load('cancellation');

        // Get the closures
        $iconClosure = $this->testClass::getTripStatusIcon();
        $colorClosure = $this->testClass::getTripStatusColor();
        $stateClosure = $this->testClass::getTripStatusState();

        // Test that regular canceled trip uses canceled icon, not no_show icon
        $icon = $iconClosure($trip);
        $color = $colorClosure($trip);
        $state = $stateClosure($trip);
        
        $this->assertEquals(TripStatus::canceled->getIcon(), $icon);
        $this->assertEquals(TripStatus::canceled->getColor(), $color);
        $this->assertEquals('Canceled by Driver', $state);
        
        // Ensure it's NOT using no_show styling
        $this->assertNotEquals(TripStatus::no_show->getIcon(), $icon);
        $this->assertNotEquals(TripStatus::no_show->getColor(), $color);
        $this->assertNotEquals('No Show', $state);
    }

    /** @test */
    public function trip_without_cancellation_uses_status_icon()
    {
        // Create a rider and driver
        $riderUser = User::factory()->create();
        $rider = Rider::factory()->create(['user_id' => $riderUser->id]);
        
        $driverUser = User::factory()->create();
        $driver = Driver::factory()->create(['user_id' => $driverUser->id]);

        // Create a completed trip
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::completed,
        ]);

        // Get the closures
        $iconClosure = $this->testClass::getTripStatusIcon();
        $colorClosure = $this->testClass::getTripStatusColor();
        $stateClosure = $this->testClass::getTripStatusState();

        // Test that trip uses its status icon
        $icon = $iconClosure($trip);
        $color = $colorClosure($trip);
        $state = $stateClosure($trip);
        
        $this->assertEquals(TripStatus::completed->getIcon(), $icon);
        $this->assertEquals(TripStatus::completed->getColor(), $color);
        $this->assertEquals(TripStatus::completed->getLabel(), $state);
    }
}
