<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Top Performers & Activity
        </x-slot>

        <div class="space-y-6">
            <!-- Performance Metrics -->
            @php
                $metrics = $this->getPerformanceMetrics();
            @endphp
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div
                    class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-600 dark:text-blue-400">Trip Growth</p>
                            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">
                                {{ $metrics['trip_growth'] >= 0 ? '+' : '' }}{{ $metrics['trip_growth'] }}%
                            </p>
                            <p class="text-xs text-blue-700 dark:text-blue-300">
                                {{ $metrics['today_trips'] }} today vs {{ $metrics['yesterday_trips'] }} yesterday
                            </p>
                        </div>
                        <x-heroicon-o-chart-bar-square class="h-8 w-8 text-blue-500" />
                    </div>
                </div>

                <div
                    class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-600 dark:text-green-400">Revenue Growth</p>
                            <p class="text-2xl font-bold text-green-900 dark:text-green-100">
                                {{ $metrics['revenue_growth'] >= 0 ? '+' : '' }}{{ $metrics['revenue_growth'] }}%
                            </p>
                            <p class="text-xs text-green-700 dark:text-green-300">
                                This week vs last week
                            </p>
                        </div>
                        <x-heroicon-o-currency-dollar class="h-8 w-8 text-green-500" />
                    </div>
                </div>

                <div
                    class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-600 dark:text-purple-400">Driver Utilization</p>
                            <p class="text-2xl font-bold text-purple-900 dark:text-purple-100">
                                {{ $metrics['driver_utilization'] }}%
                            </p>
                            <p class="text-xs text-purple-700 dark:text-purple-300">
                                {{ $metrics['utilized_drivers'] }}/{{ $metrics['active_drivers'] }} drivers active today
                            </p>
                        </div>
                        <x-heroicon-o-users class="h-8 w-8 text-purple-500" />
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Top Drivers -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <x-heroicon-o-trophy class="h-5 w-5 text-yellow-500 mr-2" />
                                Top Drivers (Last 30 Days)
                            </h3>
                            <button wire:click="exportTopDrivers"
                                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/30"
                                title="Export to CSV" wire:loading.attr="disabled"
                                wire:loading.class="opacity-50 cursor-not-allowed">
                                <x-heroicon-o-arrow-down-tray class="h-4 w-4 mr-1" />
                                <span wire:loading.remove>CSV</span>
                                <span wire:loading>Exporting...</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        @php
                            $topDrivers = $this->getTopDrivers();
                        @endphp
                        <div class="space-y-3">
                            @forelse($topDrivers as $index => $driver)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0 relative">
                                            @if($index < 3)
                                                                            <!-- Top 3 drivers with special borders and crown/medal icons -->
                                                                            <div class="relative">
                                                                                <div class="w-12 h-12 rounded-full p-0.5
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ $index === 0 ? 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600' :
                                                ($index === 1 ? 'bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500' :
                                                    'bg-gradient-to-r from-amber-500 via-amber-600 to-amber-700') }}">
                                                                                    <img src="{{ $driver['cover_picture'] ? asset('storage/' . $driver['cover_picture']) : asset('images/avatar.png') }}"
                                                                                        alt="{{ $driver['name'] }}"
                                                                                        class="w-full h-full rounded-full object-cover bg-white">
                                                                                </div>
                                                                                <!-- Crown/Medal overlay -->
                                                                                <div class="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ $index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                                                ($index === 1 ? 'bg-gradient-to-r from-gray-400 to-gray-600' :
                                                    'bg-gradient-to-r from-amber-500 to-amber-700') }}">
                                                                                    @if($index === 0)
                                                                                        👑
                                                                                    @elseif($index === 1)
                                                                                        🥈
                                                                                    @else
                                                                                        🥉
                                                                                    @endif
                                                                                </div>
                                                                            </div>
                                            @else
                                                <!-- Regular drivers with simple picture -->
                                                <div class="relative">
                                                    <div
                                                        class="w-10 h-10 rounded-full p-0.5 bg-gradient-to-r from-blue-400 to-blue-600">
                                                        <img src="{{ $driver['cover_picture'] ? asset('storage/' . $driver['cover_picture']) : asset('images/avatar.png') }}"
                                                            alt="{{ $driver['name'] }}"
                                                            class="w-full h-full rounded-full object-cover bg-white">
                                                    </div>
                                                    <!-- Position number overlay -->
                                                    <div
                                                        class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-xs shadow-lg">
                                                        {{ $index + 1 }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $driver['name'] }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $driver['trips_count'] }} trips • ⭐
                                                {{ number_format($driver['average_rating'], 1) }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-green-600 dark:text-green-400">
                                            {{ number_format($driver['total_revenue'], 2) }} LYD
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-gray-500 dark:text-gray-400 text-center py-4">No driver data available</p>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Top Riders -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <x-heroicon-o-trophy class="h-5 w-5 text-yellow-500 mr-2" />
                                Top Riders (Last 30 Days)
                            </h3>
                            <button wire:click="exportTopRiders"
                                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/30"
                                title="Export to CSV" wire:loading.attr="disabled"
                                wire:loading.class="opacity-50 cursor-not-allowed">
                                <x-heroicon-o-arrow-down-tray class="h-4 w-4 mr-1" />
                                <span wire:loading.remove>CSV</span>
                                <span wire:loading>Exporting...</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        @php
                            $topRiders = $this->getTopRiders();
                        @endphp
                        <div class="space-y-3">
                            @forelse($topRiders as $index => $rider)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0 relative">
                                            @if($index < 3)
                                                                            <!-- Top 3 riders with special borders and crown/medal icons -->
                                                                            <div class="relative">
                                                                                <div class="w-12 h-12 rounded-full p-0.5
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ $index === 0 ? 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600' :
                                                ($index === 1 ? 'bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500' :
                                                    'bg-gradient-to-r from-amber-500 via-amber-600 to-amber-700') }}">
                                                                                    <img src="{{ $rider['cover_picture'] ? asset('storage/' . $rider['cover_picture']) : asset('images/avatar.png') }}"
                                                                                        alt="{{ $rider['name'] }}"
                                                                                        class="w-full h-full rounded-full object-cover bg-white">
                                                                                </div>
                                                                                <!-- Crown/Medal overlay -->
                                                                                <div class="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-white font-bold text-xs shadow-lg
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    {{ $index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                                                ($index === 1 ? 'bg-gradient-to-r from-gray-400 to-gray-600' :
                                                    'bg-gradient-to-r from-amber-500 to-amber-700') }}">
                                                                                    @if($index === 0)
                                                                                        👑
                                                                                    @elseif($index === 1)
                                                                                        🥈
                                                                                    @else
                                                                                        🥉
                                                                                    @endif
                                                                                </div>
                                                                            </div>
                                            @else
                                                <!-- Regular riders with simple picture -->
                                                <div class="relative">
                                                    <div
                                                        class="w-10 h-10 rounded-full p-0.5 bg-gradient-to-r from-blue-400 to-blue-600">
                                                        <img src="{{ $rider['cover_picture'] ? asset('storage/' . $rider['cover_picture']) : asset('images/avatar.png') }}"
                                                            alt="{{ $rider['name'] }}"
                                                            class="w-full h-full rounded-full object-cover bg-white">
                                                    </div>
                                                    <!-- Position number overlay -->
                                                    <div
                                                        class="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-xs shadow-lg">
                                                        {{ $index + 1 }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $rider['name'] }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $rider['trips_count'] }} trips • ⭐
                                                {{ number_format($rider['average_rating'], 1) }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-green-600 dark:text-green-400">
                                            {{ number_format($rider['total_revenue'], 2) }} LYD
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-gray-500 dark:text-gray-400 text-center py-4">No rider data available</p>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Top Areas -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <x-heroicon-o-map-pin class="h-5 w-5 text-red-500 mr-2" />
                                Top Areas (Last 30 Days)
                            </h3>
                            <button wire:click="exportTopAreas"
                                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/30"
                                title="Export to CSV" wire:loading.attr="disabled"
                                wire:loading.class="opacity-50 cursor-not-allowed">
                                <x-heroicon-o-arrow-down-tray class="h-4 w-4 mr-1" />
                                <span wire:loading.remove>CSV</span>
                                <span wire:loading>Exporting...</span>
                            </button>
                        </div>
                    </div>
                    <div class="p-4">
                        @php
                            $topAreas = $this->getTopAreas();
                        @endphp
                        <div class="space-y-3">
                            @forelse($topAreas as $index => $area)
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0">
                                            <div
                                                class="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold text-sm">
                                                {{ $index + 1 }}
                                            </div>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $area['name'] }}</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $area['trips_count'] }} trips • {{ $area['completion_rate'] }}%
                                                completion
                                            </p>
                                            <p class="text-xs text-gray-400 dark:text-gray-500">
                                                Avg: {{ number_format($area['avg_revenue_per_trip'], 2) }} LYD/trip
                                            </p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-green-600 dark:text-green-400">
                                            {{ number_format($area['total_revenue'], 2) }} LYD
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <p class="text-gray-500 dark:text-gray-400 text-center py-4">No area data available</p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </x-filament::section>
</x-filament-widgets::widget>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Function to handle CSV download
            function downloadCsv(data, filename) {
                try {
                    // Create a blob with the CSV data
                    const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });

                    // Create a temporary link element
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);

                    link.setAttribute('href', url);
                    link.setAttribute('download', filename);
                    link.style.visibility = 'hidden';

                    // Add to DOM, click, and remove
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Clean up the URL object
                    URL.revokeObjectURL(url);

                    console.log('CSV download initiated:', filename);
                } catch (error) {
                    console.error('Error downloading CSV:', error);
                }
            }

            // Setup Livewire event listeners
            function setupLivewireListeners() {
                if (typeof Livewire !== 'undefined') {
                    // Livewire v3 style
                    Livewire.on('download-csv', (event) => {
                        console.log('Livewire download-csv event received:', event);

                        let data, filename;

                        // Handle different event formats
                        if (event && typeof event === 'object') {
                            if (event.data && event.filename) {
                                data = event.data;
                                filename = event.filename;
                            } else if (Array.isArray(event) && event[0]) {
                                data = event[0].data;
                                filename = event[0].filename;
                            } else if (event[0] && event[1]) {
                                data = event[0];
                                filename = event[1];
                            }
                        }

                        if (data && filename) {
                            downloadCsv(data, filename);
                        } else {
                            console.error('Invalid event data:', event);
                        }
                    });
                }
            }

            // Try to setup listeners immediately
            setupLivewireListeners();

            // Also try after Livewire loads
            document.addEventListener('livewire:init', setupLivewireListeners);
            document.addEventListener('livewire:load', setupLivewireListeners);

            // Fallback for direct document events
            document.addEventListener('livewire:download-csv', function (event) {
                console.log('Document livewire:download-csv event received:', event);
                const data = event.detail?.data || event.detail?.[0]?.data;
                const filename = event.detail?.filename || event.detail?.[0]?.filename;

                if (data && filename) {
                    downloadCsv(data, filename);
                }
            });
        });
    </script>
@endpush