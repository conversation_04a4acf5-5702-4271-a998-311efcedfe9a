<?php

use App\Console\Commands\ExpiredOtpAttempts;
use App\Console\Commands\ResetWeeklyCancellations;
use App\Jobs\CheckTripHeartbeats;
use App\Jobs\DriverDocumentsExpiryReminder;
use App\Jobs\VehicleDocumentsExpiryReminder;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('otp:clean')->daily();

Schedule::command('sanctum:prune-expired --hours=24')->daily();

Schedule::command(ExpiredOtpAttempts::class)->dailyAt('00:00')
    ->timezone('Africa/Tripoli');

Schedule::job(new DriverDocumentsExpiryReminder)->everyTwoMinutes()
    ->timezone('Africa/Tunis');

Schedule::job(new VehicleDocumentsExpiryReminder)->everyTwoMinutes()
    ->timezone('Africa/Tunis');

Schedule::command(ResetWeeklyCancellations::class)
    ->weeklyOn(6, '00:00')
    ->timezone('Africa/Tunis');

// Schedule::job(new CheckTripHeartbeats)->everyMinute();
