<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRuleNonOperationalPeriod;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Contracts\View\View;
use Illuminate\Support\HtmlString;
use Livewire\Component;

class GlobalTime extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public PricingRuleAdditionalDayCharge $record;

    public $dayCharges;

    protected $listeners = ['pricing-rules-updated' => 'refreshData', 'non-operational-periods-updated' => 'refreshData'];

    public function mount(): void
    {
        // Get or create the global time settings record
        $this->record = PricingRuleAdditionalDayCharge::firstOrCreate(
            ['id' => 1, 'pricing_rule_id' => 1],
            [
                'day' => 'monday',
                'day_start_at' => '06:00',
                'day_end_at' => '18:00',
                'night_start_at' => '18:00',
                'night_end_at' => '06:00',
                'day_charge_type' => 'fixed',
                'night_charge_type' => 'fixed',
                'day_fixed_charge' => 0.00,
                'day_percentage_charge' => 0.00,
                'night_fixed_charge' => 0.00,
                'night_percentage_charge' => 0.00,
                'day_distance_charge_type' => 'fixed',
                'day_distance_fixed_charge' => 0.00,
                'day_distance_percentage_charge' => 0.00,
                'night_distance_charge_type' => 'fixed',
                'night_distance_fixed_charge' => 0.00,
                'night_distance_percentage_charge' => 0.00,
            ]
        );

        // Initialize the form with both the original fields and our new hour/minute fields
        $recordArray = $this->record->attributesToArray();

        // Add the custom fields for day and night start/end times
        $recordArray['day_start_hour'] = explode(':', $this->record->day_start_at)[0];
        $recordArray['day_start_minute'] = explode(':', $this->record->day_start_at)[1];
        $recordArray['day_end_hour'] = explode(':', $this->record->day_end_at)[0];
        $recordArray['day_end_minute'] = explode(':', $this->record->day_end_at)[1];
        $recordArray['night_start_hour'] = explode(':', $this->record->night_start_at)[0];
        $recordArray['night_start_minute'] = explode(':', $this->record->night_start_at)[1];
        $recordArray['night_end_hour'] = explode(':', $this->record->night_end_at)[0];
        $recordArray['night_end_minute'] = explode(':', $this->record->night_end_at)[1];

        $this->form->fill($recordArray);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Time Settings')
                    ->icon('heroicon-s-clock')
                    ->iconColor('warning')
                    ->description(fn () => new HtmlString(
                        'Configure global day and night time periods for pricing rules.<br>'.
                        '<b>'.$this->getFormattedPeriodsProperty().'</b>'
                    ))
                    ->schema([
                        // Day Section
                        Section::make('Day Time Period')
                            ->icon('heroicon-o-sun')
                            ->iconcolor('amber')
                            ->description('Set the start and end times for the day period.')
                            ->extraAttributes(['class' => 'bg-indigo-50 border border-indigo-200 rounded-xl p-4'])
                            ->schema([
                                // Day Start Time
                                Fieldset::make('Start Time')
                                    ->columnSpan(1)
                                    ->columns(13)
                                    ->extraAttributes(['class' => 'p-2'])
                                    ->schema([
                                        Select::make('day_start_hour')
                                            ->hiddenLabel()
                                            ->placeholder('Hour')
                                            ->required()
                                            ->columnSpan(5)
                                            ->reactive()
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options(fn () => array_combine(range(0, 23), array_map(fn ($hour) => sprintf('%02d', $hour), range(0, 23))))
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Update day_start_at for saving
                                                $minute = $get('day_start_minute') ?? '00';
                                                $set('day_start_at', sprintf('%02d:%s', $state, $minute));
                                            }),

                                        Placeholder::make('day_start_separator')
                                            ->content(fn () => new HtmlString('<div class="flex justify-center items-center w-full px-2 sm:px-3"><span class="text-xl font-bold">:</span></div>'))
                                            ->hiddenLabel()
                                            ->columnSpan(3),

                                        Select::make('day_start_minute')
                                            ->hiddenLabel()
                                            ->placeholder('Minute')
                                            ->required()
                                            ->columnSpan(5)
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options([
                                                '00' => '00',
                                                '15' => '15',
                                                '30' => '30',
                                                '45' => '45',
                                            ])
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $hour = $get('day_start_hour') ?? '00';
                                                $set('day_start_at', sprintf('%02d:%s', $hour, $state));
                                            }),

                                        Hidden::make('day_start_at')->live(),
                                    ]),

                                // Day End Time
                                Fieldset::make('End Time')
                                    ->columnSpan(1)
                                    ->columns(13)
                                    ->extraAttributes(['class' => 'p-2'])
                                    ->schema([
                                        Select::make('day_end_hour')
                                            ->hiddenLabel()
                                            ->placeholder('Hour')
                                            ->required()
                                            ->columnSpan(5)
                                            ->reactive()
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options(fn () => array_combine(range(0, 23), array_map(fn ($hour) => sprintf('%02d', $hour), range(0, 23))))
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Update day_end_at for saving
                                                $minute = $get('day_end_minute') ?? '00';
                                                $set('day_end_at', sprintf('%02d:%s', $state, $minute));
                                            }),

                                        Placeholder::make('day_end_separator')
                                            ->content(fn () => new HtmlString('<div class="flex justify-center items-center w-full px-2 sm:px-3"><span class="text-xl font-bold">:</span></div>'))
                                            ->hiddenLabel()
                                            ->columnSpan(3),

                                        Select::make('day_end_minute')
                                            ->hiddenLabel()
                                            ->placeholder('Minute')
                                            ->required()
                                            ->columnSpan(5)
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options([
                                                '00' => '00',
                                                '15' => '15',
                                                '30' => '30',
                                                '45' => '45',
                                            ])
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $hour = $get('day_end_hour') ?? '00';
                                                $set('day_end_at', sprintf('%02d:%s', $hour, $state));
                                            }),

                                        Hidden::make('day_end_at')->live(),
                                    ]),
                            ])->columns(2),

                        // Night Section
                        Section::make('Night Time Period')
                            ->icon('heroicon-o-moon')
                            ->iconcolor('indigo')
                            ->description('Set the start and end times for the night period.')
                            ->extraAttributes(['class' => 'bg-indigo-50 border border-indigo-200 rounded-xl p-4'])
                            ->schema([
                                // Night Start Time
                                Fieldset::make('Start Time')
                                    ->columnSpan(1)
                                    ->columns(13)
                                    ->extraAttributes(['class' => 'p-2'])
                                    ->schema([
                                        Select::make('night_start_hour')
                                            ->hiddenLabel()
                                            ->placeholder('Hour')
                                            ->required()
                                            ->columnSpan(5)
                                            ->reactive()
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options(fn () => array_combine(range(0, 23), array_map(fn ($hour) => sprintf('%02d', $hour), range(0, 23))))
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Update night_start_at for saving
                                                $minute = $get('night_start_minute') ?? '00';
                                                $set('night_start_at', sprintf('%02d:%s', $state, $minute));
                                            }),

                                        Placeholder::make('night_start_separator')
                                            ->content(fn () => new HtmlString('<div class="flex justify-center items-center w-full px-2 sm:px-3"><span class="text-xl font-bold">:</span></div>'))
                                            ->hiddenLabel()
                                            ->columnSpan(3),

                                        Select::make('night_start_minute')
                                            ->hiddenLabel()
                                            ->placeholder('Minute')
                                            ->required()
                                            ->columnSpan(5)
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options([
                                                '00' => '00',
                                                '15' => '15',
                                                '30' => '30',
                                                '45' => '45',
                                            ])
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $hour = $get('night_start_hour') ?? '00';
                                                $set('night_start_at', sprintf('%02d:%s', $hour, $state));
                                            }),

                                        Hidden::make('night_start_at')->live(),
                                    ]),

                                // Night End Time
                                Fieldset::make('End Time')
                                    ->columnSpan(1)
                                    ->columns(13)
                                    ->extraAttributes(['class' => 'p-2'])
                                    ->schema([
                                        Select::make('night_end_hour')
                                            ->hiddenLabel()
                                            ->placeholder('Hour')
                                            ->required()
                                            ->columnSpan(5)
                                            ->reactive()
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options(fn () => array_combine(range(0, 23), array_map(fn ($hour) => sprintf('%02d', $hour), range(0, 23))))
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                // Update night_end_at for saving
                                                $minute = $get('night_end_minute') ?? '00';
                                                $set('night_end_at', sprintf('%02d:%s', $state, $minute));
                                            }),

                                        Placeholder::make('night_end_separator')
                                            ->content(fn () => new HtmlString('<div class="flex justify-center items-center w-full px-2 sm:px-3"><span class="text-xl font-bold">:</span></div>'))
                                            ->hiddenLabel()
                                            ->columnSpan(3),

                                        Select::make('night_end_minute')
                                            ->hiddenLabel()
                                            ->placeholder('Minute')
                                            ->required()
                                            ->columnSpan(5)
                                            ->native(false)
                                            ->extraAttributes([
                                                'class' => 'min-w-[80px] sm:min-w-[100px]',
                                                'style' => 'width: 100%; min-width: 80px;',
                                            ])
                                            ->options([
                                                '00' => '00',
                                                '15' => '15',
                                                '30' => '30',
                                                '45' => '45',
                                            ])
                                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                                $hour = $get('night_end_hour') ?? '00';
                                                $set('night_end_at', sprintf('%02d:%s', $hour, $state));
                                            }),

                                        Hidden::make('night_end_at')->live(),
                                    ]),
                            ])->columns(2),
                    ])->columns(1),
            ])
            ->statePath('data')
            ->model($this->record);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        // Validate that the day and night times do not overlap
        $dayStartTime = $data['day_start_at'];
        $dayEndTime = $data['day_end_at'];
        $nightStartTime = $data['night_start_at'];
        $nightEndTime = $data['night_end_at'];

        // First check: Day must be a valid period (start < end)
        if ($dayStartTime >= $dayEndTime) {
            Notification::make()
                ->title('Invalid Day Time Range')
                ->body('The day start time must be earlier than the day end time.')
                ->danger()
                ->send();

            return;
        }

        // Special case for night period: It can wrap around midnight
        // Check if night is a wrap-around period (night end < night start)
        $isNightWrapAround = $nightEndTime <= $nightStartTime;

        // Check for overlap based on whether night wraps around midnight or not
        $hasOverlap = false;

        if ($isNightWrapAround) {
            // For wrap-around night periods (e.g., 22:00 to 06:00)
            // Overlap occurs if day period is not entirely within the "gap" between night end and night start
            // This means: NOT (day_start >= night_end AND day_end <= night_start)
            if (! ($dayStartTime >= $nightEndTime && $dayEndTime <= $nightStartTime)) {
                $hasOverlap = true;
            }
        } else {
            // For regular night periods (e.g., 02:00 to 08:00)
            // Overlap occurs if periods intersect:
            // This means: NOT (day_end <= night_start OR day_start >= night_end)
            if (! ($dayEndTime <= $nightStartTime || $dayStartTime >= $nightEndTime)) {
                $hasOverlap = true;
            }
        }

        if ($hasOverlap) {
            Notification::make()
                ->title('Time Overlap Error')
                ->body('The night period cannot overlap with the day period.')
                ->danger()
                ->send();

            return;
        }

        // Update the global record
        $this->record->update([
            'day_start_at' => $data['day_start_at'],
            'day_end_at' => $data['day_end_at'],
            'night_start_at' => $data['night_start_at'],
            'night_end_at' => $data['night_end_at'],
            // Preserve existing values for required fields
            'day_charge_type' => $this->record->day_charge_type,
            'night_charge_type' => $this->record->night_charge_type,
        ]);

        // Days of the week to update
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        // Update each day's timing based on the global time
        foreach ($days as $day) {
            $dayRecord = PricingRuleAdditionalDayCharge::where('pricing_rule_id', $this->record->pricing_rule_id)
                ->where('day', $day)
                ->first();

            if ($dayRecord) {
                $dayRecord->update([
                    'day_start_at' => $data['day_start_at'],
                    'day_end_at' => $data['day_end_at'],
                    'night_start_at' => $data['night_start_at'],
                    'night_end_at' => $data['night_end_at'],
                    // Preserve existing values for required fields
                    'day_charge_type' => $dayRecord->day_charge_type,
                    'night_charge_type' => $dayRecord->night_charge_type,
                ]);
            } else {
                // Create a new record for this day if it doesn't exist
                PricingRuleAdditionalDayCharge::create([
                    'pricing_rule_id' => $this->record->pricing_rule_id,
                    'day' => $day,
                    'day_start_at' => $data['day_start_at'],
                    'day_end_at' => $data['day_end_at'],
                    'night_start_at' => $data['night_start_at'],
                    'night_end_at' => $data['night_end_at'],
                    'day_charge_type' => 'fixed',
                    'night_charge_type' => 'fixed',
                    'day_fixed_charge' => 0.00,
                    'day_percentage_charge' => 0.00,
                    'night_fixed_charge' => 0.00,
                    'night_percentage_charge' => 0.00,
                    'day_distance_charge_type' => 'fixed',
                    'day_distance_fixed_charge' => 0.00,
                    'day_distance_percentage_charge' => 0.00,
                    'night_distance_charge_type' => 'fixed',
                    'night_distance_fixed_charge' => 0.00,
                    'night_distance_percentage_charge' => 0.00,
                ]);
            }
        }

        // Get the fresh record
        $this->record = $this->record->fresh();

        // Prepare data for form refresh
        $recordArray = $this->record->attributesToArray();

        // Add the hour and minute fields back
        $recordArray['day_start_hour'] = explode(':', $this->record->day_start_at)[0];
        $recordArray['day_start_minute'] = explode(':', $this->record->day_start_at)[1];
        $recordArray['day_end_hour'] = explode(':', $this->record->day_end_at)[0];
        $recordArray['day_end_minute'] = explode(':', $this->record->day_end_at)[1];
        $recordArray['night_start_hour'] = explode(':', $this->record->night_start_at)[0];
        $recordArray['night_start_minute'] = explode(':', $this->record->night_start_at)[1];
        $recordArray['night_end_hour'] = explode(':', $this->record->night_end_at)[0];
        $recordArray['night_end_minute'] = explode(':', $this->record->night_end_at)[1];

        // Refresh form with all fields
        $this->form->fill($recordArray);

        // Calculate and update non-operational periods
        $nonOperationalPeriods = $this->calculateAndUpdateNonOperationalPeriods($data);
        $this->refreshData();
        // Emit event
        $this->dispatch('time-settings-updated');
        $this->dispatch('non-operational-periods-updated');

        // Create notification with non-operational periods information
        $this->sendSuccessNotificationWithPeriods($nonOperationalPeriods);
    }

    /**
     * Calculate non-operational periods based on gaps between day and night operational hours
     */
    private function calculateAndUpdateNonOperationalPeriods(array $data): array
    {
        $dayStartTime = $data['day_start_at'];
        $dayEndTime = $data['day_end_at'];
        $nightStartTime = $data['night_start_at'];
        $nightEndTime = $data['night_end_at'];

        // Clear existing non-operational periods for all day charges
        $dayCharges = PricingRuleAdditionalDayCharge::where('pricing_rule_id', $this->record->pricing_rule_id)->get();
        foreach ($dayCharges as $dayCharge) {
            PricingRuleNonOperationalPeriod::where('day_charge_id', $dayCharge->id)->delete();
        }

        // Calculate non-operational periods (gaps between operational hours)
        $nonOperationalPeriods = $this->calculateNonOperationalGaps($dayStartTime, $dayEndTime, $nightStartTime, $nightEndTime);

        // Insert non-operational periods for each day
        foreach ($dayCharges as $dayCharge) {
            foreach ($nonOperationalPeriods as $period) {
                // Skip invalid periods where start equals end
                if ($period['start_at'] === $period['end_at']) {
                    continue;
                }

                PricingRuleNonOperationalPeriod::create([
                    'day_charge_id' => $dayCharge->id,
                    'start_at' => $period['start_at'],
                    'end_at' => $period['end_at'],
                ]);
            }
        }

        return $nonOperationalPeriods;
    }

    /**
     * Calculate the gaps between operational hours to determine non-operational periods
     */
    private function calculateNonOperationalGaps(string $dayStart, string $dayEnd, string $nightStart, string $nightEnd): array
    {
        $periods = [];

        // Convert times to minutes for easier calculation
        $dayStartMinutes = $this->timeToMinutes($dayStart);
        $dayEndMinutes = $this->timeToMinutes($dayEnd);
        $nightStartMinutes = $this->timeToMinutes($nightStart);
        $nightEndMinutes = $this->timeToMinutes($nightEnd);

        // Check if night wraps around midnight
        $isNightWrapAround = $nightEndMinutes <= $nightStartMinutes;

        if ($isNightWrapAround) {
            // Night wraps around midnight (e.g., 23:00 to 06:00)
            if ($dayEndMinutes < $nightStartMinutes) {
                $periods[] = [
                    'start_at' => $this->minutesToTime($dayEndMinutes),
                    'end_at' => $this->minutesToTime($nightStartMinutes),
                ];
            }

            if ($nightEndMinutes < $dayStartMinutes) {
                $periods[] = [
                    'start_at' => $this->minutesToTime($nightEndMinutes),
                    'end_at' => $this->minutesToTime($dayStartMinutes),
                ];
            }
        } else {

            if ($dayEndMinutes < $nightStartMinutes) {
                $periods[] = [
                    'start_at' => $this->minutesToTime($dayEndMinutes),
                    'end_at' => $this->minutesToTime($nightStartMinutes),
                ];
            }

            $overnightGapExists = ($nightEndMinutes < 1440) || ($dayStartMinutes > 0);

            if ($overnightGapExists) {

                $overnightStart = $this->minutesToTime($nightEndMinutes);
                $overnightEnd = $this->minutesToTime($dayStartMinutes);

                $periods[] = [
                    'start_at' => $overnightStart,
                    'end_at' => $overnightEnd,
                    'spans_midnight' => true, // Flag to indicate this period crosses midnight
                ];
            }
        }

        return $periods;
    }

    /**
     * Convert time string (HH:MM) to minutes since midnight
     */
    private function timeToMinutes(string $time): int
    {
        [$hours, $minutes] = explode(':', $time);

        return (int) $hours * 60 + (int) $minutes;
    }

    /**
     * Convert minutes since midnight to time string (HH:MM)
     */
    private function minutesToTime(int $minutes): string
    {
        $hours = intval($minutes / 60);
        $mins = $minutes % 60;

        return sprintf('%02d:%02d', $hours, $mins);
    }

    /**
     * Send success notification with non-operational periods information
     */
    private function sendSuccessNotificationWithPeriods(array $nonOperationalPeriods): void
    {
        // First notification: Success message
        Notification::make()
            ->title('Time Settings Updated')
            ->body('Global time settings have been successfully updated and applied to all days.')
            ->success()
            ->duration(4000)
            ->send();

        // Second notification: Non-operational periods information
        if (empty($nonOperationalPeriods)) {
            // No non-operational periods - service is available 24/7
            Notification::make()
                ->title('Service Availability')
                ->body('The app will be available 24/7 with no non operational periods.')
                ->info()
                ->duration(5000)
                ->send();
        } else {
            // Format the non-operational periods for display
            $formattedPeriods = collect($nonOperationalPeriods)->map(function ($period) {
                $startTime = \Carbon\Carbon::parse($period['start_at'])->format('H:i');
                $endTime = \Carbon\Carbon::parse($period['end_at'])->format('H:i');

                // Check if this period spans midnight (end time is earlier than start time)
                if ($endTime <= $startTime) {
                    return "{$startTime} to {$endTime} (next day)";
                }

                return "{$startTime} to {$endTime}";
            });

            $periodsText = $formattedPeriods->join(' and ');

            Notification::make()
                ->title('Service Interruption')
                ->body("The app will not be available from {$periodsText}.")
                ->warning()
                ->duration(6000)
                ->send();
        }
    }

    public function refreshData()
    {
        $this->loadDayCharges();
    }

    /**
     * Load day charges with their non-operational periods
     */
    private function loadDayCharges()
    {
        // Get the pricing rule first
        $pricingRule = \App\Models\PricingRules::firstOrCreate(
            ['id' => 1],
            [
                'global_base_price' => 5.00,
                'global_price_per_km' => 5.00,
                'time_threshold_percentage' => 0.00,
            ]
        );

        // Load all day charges with their non-operational periods
        $this->dayCharges = PricingRuleAdditionalDayCharge::with('nonOperationalPeriods')
            ->where('pricing_rule_id', $pricingRule->id)
            ->get();
    }

    /**
     * Get formatted non-operational periods for display
     */
    public function getFormattedPeriodsProperty()
    {
        $this->loadDayCharges();
        $dayCharges = $this->dayCharges;

        if ($dayCharges->isEmpty()) {
            return 'No day configurations found - Please configure operational hours first';
        }

        // Collect all unique non-operational periods across all days
        $allPeriods = collect();

        foreach ($dayCharges as $dayCharge) {
            foreach ($dayCharge->nonOperationalPeriods as $period) {
                $periodKey = $period->start_at.'-'.$period->end_at;
                if (! $allPeriods->has($periodKey)) {
                    $allPeriods->put($periodKey, [
                        'start_at' => $period->start_at,
                        'end_at' => $period->end_at,
                    ]);
                }
            }
        }

        if ($allPeriods->isEmpty()) {
            return 'Service available 24/7 - No non-operational periods defined';
        }

        $formattedPeriods = $allPeriods->map(function ($period) {
            $startTime = \Carbon\Carbon::parse($period['start_at'])->format('H:i');
            $endTime = \Carbon\Carbon::parse($period['end_at'])->format('H:i');

            // Check if this period spans midnight (end time is earlier than start time)
            if ($endTime <= $startTime) {
                return "{$startTime} - {$endTime} (next day)";
            }

            return "{$startTime} - {$endTime}";
        });

        return 'Service unavailable during: '.$formattedPeriods->join(', ');
    }

    public function render(): View
    {
        return view('livewire.pricing-rules.global-time');
    }
}
