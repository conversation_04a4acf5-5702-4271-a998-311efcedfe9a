<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Area;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Filament\Widgets\Widget;

class TopPerformersWidget extends Widget
{
    use CalculatesRevenue;

    protected static string $view = 'filament.resources.report-resource.widgets.top-performers';

    protected static ?string $pollingInterval = '300s'; // 5 minutes

    protected int|string|array $columnSpan = 'full';

    protected static bool $isLazy = false;

    public function getTopDrivers(): array
    {
        $startDate = Carbon::now()->subDays(30);

        return Driver::with(['user', 'trips' => function ($query) use ($startDate) {
            $query->where('status', 'completed')
                ->where('created_at', '>=', $startDate);
        }])
            ->whereHas('trips', function ($query) use ($startDate) {
                $query->where('status', 'completed')
                    ->where('created_at', '>=', $startDate);
            })
            ->get()
            ->map(function ($driver) {
                $completedTrips = $driver->trips;
                $totalRevenue = $completedTrips->sum(function ($trip) {
                    $pricing = json_decode($trip->pricing_breakdown, true);

                    return $pricing['total'] ?? 0;
                });

                return [
                    'id' => $driver->id,
                    'name' => $driver->user->name.' '.$driver->user->last_name,
                    'phone' => $driver->user->phone_number,
                    'trips_count' => $completedTrips->count(),
                    'total_revenue' => $totalRevenue,
                    'average_rating' => $driver->average_driver_rating ?? 0,
                    'status' => $driver->global_status,
                    'cover_picture' => $driver->user->cover_picture,
                ];
            })
            ->sortByDesc('total_revenue')
            ->take(10)
            ->values()
            ->toArray();
    }

    public function getTopRiders(): array
    {
        $startDate = Carbon::now()->subDays(30);

        return Rider::with(['user', 'trips' => function ($query) use ($startDate) {
            $query->where('status', 'completed')
                ->where('created_at', '>=', $startDate);
        }])
            ->whereHas('trips', function ($query) use ($startDate) {
                $query->where('status', 'completed')
                    ->where('created_at', '>=', $startDate);
            })
            ->get()
            ->map(function ($rider) {
                $completedTrips = $rider->trips;
                $totalRevenue = $completedTrips->sum(function ($trip) {
                    $pricing = json_decode($trip->pricing_breakdown, true);

                    return $pricing['total'] ?? 0;
                });

                return [
                    'id' => $rider->id,
                    'name' => $rider->user->name.' '.$rider->user->last_name,
                    'phone' => $rider->user->phone_number,
                    'trips_count' => $completedTrips->count(),
                    'total_revenue' => $totalRevenue,
                    'average_rating' => $rider->average_rider_rating ?? 0,
                    'status' => $rider->global_status,
                    'cover_picture' => $rider->user->cover_picture,
                ];
            })
            ->sortByDesc('total_revenue')
            ->take(10)
            ->values()
            ->toArray();
    }

    public function getTopAreas(): array
    {
        $startDate = Carbon::now()->subDays(30);

        return Area::with(['departureTrips' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate)
                ->select('id', 'departure_area_id', 'status', 'pricing_breakdown', 'created_at');
        }])
            ->whereHas('departureTrips', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })
            ->where('is_active', true) // Only show active areas
            ->select('id', 'name_en', 'is_active') // Only select needed columns
            ->get()
            ->map(function ($area) {
                $trips = $area->departureTrips;
                $completedTrips = $trips->where('status', 'completed');

                // Calculate total revenue from completed trips only
                $totalRevenue = $completedTrips->sum(function ($trip) {
                    $pricing = json_decode($trip->pricing_breakdown, true);

                    return $pricing['total'] ?? 0;
                });

                // Calculate average revenue per trip
                $avgRevenuePerTrip = $completedTrips->count() > 0
                    ? $totalRevenue / $completedTrips->count()
                    : 0;

                return [
                    'id' => $area->id,
                    'name' => $area->name_en,
                    'trips_count' => $trips->count(),
                    'completed_trips' => $completedTrips->count(),
                    'total_revenue' => round($totalRevenue, 2),
                    'avg_revenue_per_trip' => round($avgRevenuePerTrip, 2),
                    'completion_rate' => $trips->count() > 0
                        ? round(($completedTrips->count() / $trips->count()) * 100, 1)
                        : 0,
                    'is_active' => $area->is_active,
                ];
            })
            ->filter(function ($area) {
                // Only include areas with at least 1 trip
                return $area['trips_count'] > 0;
            })
            ->sortByDesc('total_revenue') // Sort by total revenue (most active by revenue)
            ->take(8)
            ->values()
            ->toArray();
    }

    public function getPerformanceMetrics(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisWeek = Carbon::now()->startOfWeek();
        $lastWeek = Carbon::now()->subWeek()->startOfWeek();

        // Today vs Yesterday
        $todayTrips = Trip::whereDate('created_at', $today)->count();
        $yesterdayTrips = Trip::whereDate('created_at', $yesterday)->count();
        $tripGrowth = $yesterdayTrips > 0
            ? round((($todayTrips - $yesterdayTrips) / $yesterdayTrips) * 100, 1)
            : ($todayTrips > 0 ? 100 : 0);

        // This week vs Last week
        $thisWeekRevenue = $this->calculateTotalRevenue(
            Trip::whereBetween('created_at', [$thisWeek, Carbon::now()])
        );
        $lastWeekRevenue = $this->calculateTotalRevenue(
            Trip::whereBetween('created_at', [$lastWeek, $thisWeek->copy()->subSecond()])
        );
        $revenueGrowth = $lastWeekRevenue > 0
            ? round((($thisWeekRevenue - $lastWeekRevenue) / $lastWeekRevenue) * 100, 1)
            : ($thisWeekRevenue > 0 ? 100 : 0);

        // Driver efficiency
        $activeDrivers = Driver::where('global_status', 'active')->count();
        $driversWithTripsToday = Driver::whereHas('trips', function ($query) use ($today) {
            $query->whereDate('created_at', $today);
        })->count();
        $driverUtilization = $activeDrivers > 0
            ? round(($driversWithTripsToday / $activeDrivers) * 100, 1)
            : 0;

        return [
            'trip_growth' => $tripGrowth,
            'revenue_growth' => $revenueGrowth,
            'driver_utilization' => $driverUtilization,
            'today_trips' => $todayTrips,
            'yesterday_trips' => $yesterdayTrips,
            'this_week_revenue' => $thisWeekRevenue,
            'last_week_revenue' => $lastWeekRevenue,
            'active_drivers' => $activeDrivers,
            'utilized_drivers' => $driversWithTripsToday,
        ];
    }

    public function exportTopDrivers()
    {
        try {
            $drivers = $this->getTopDrivers();

            $csvData = "Rank,Name,Phone,Trips Count,Total Revenue (LYD),Average Rating,Status\n";

            foreach ($drivers as $index => $driver) {
                $csvData .= sprintf(
                    "%d,\"%s\",\"%s\",%d,%.2f,%.1f,\"%s\"\n",
                    $index + 1,
                    $driver['name'],
                    $driver['phone'],
                    $driver['trips_count'],
                    $driver['total_revenue'],
                    $driver['average_rating'],
                    $driver['status']->value ?? 'Unknown'
                );
            }

            $this->downloadCsv($csvData, 'top-drivers-'.date('Y-m-d').'.csv');
        } catch (\Exception $e) {
            \Log::error('Error exporting top drivers: '.$e->getMessage());
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to export CSV. Please try again.',
            ]);
        }
    }

    public function exportTopRiders()
    {
        try {
            $riders = $this->getTopRiders();

            $csvData = "Rank,Name,Phone,Trips Count,Total Revenue (LYD),Average Rating,Status\n";

            foreach ($riders as $index => $rider) {
                $csvData .= sprintf(
                    "%d,\"%s\",\"%s\",%d,%.2f,%.1f,\"%s\"\n",
                    $index + 1,
                    $rider['name'],
                    $rider['phone'],
                    $rider['trips_count'],
                    $rider['total_revenue'],
                    $rider['average_rating'],
                    $rider['status']->value ?? 'Unknown'
                );
            }

            $this->downloadCsv($csvData, 'top-riders-'.date('Y-m-d').'.csv');
        } catch (\Exception $e) {
            \Log::error('Error exporting top riders: '.$e->getMessage());
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to export CSV. Please try again.',
            ]);
        }
    }

    public function exportTopAreas()
    {
        try {
            $areas = $this->getTopAreas();

            $csvData = "Rank,Area Name,Trips Count,Completed Trips,Total Revenue (LYD),Avg Revenue Per Trip (LYD),Completion Rate (%)\n";

            foreach ($areas as $index => $area) {
                $csvData .= sprintf(
                    "%d,\"%s\",%d,%d,%.2f,%.2f,%.1f\n",
                    $index + 1,
                    $area['name'],
                    $area['trips_count'],
                    $area['completed_trips'],
                    $area['total_revenue'],
                    $area['avg_revenue_per_trip'],
                    $area['completion_rate']
                );
            }

            $this->downloadCsv($csvData, 'top-areas-'.date('Y-m-d').'.csv');
        } catch (\Exception $e) {
            \Log::error('Error exporting top areas: '.$e->getMessage());
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to export CSV. Please try again.',
            ]);
        }
    }

    private function downloadCsv($csvData, $filename)
    {
        try {
            \Log::info('Dispatching CSV download', [
                'filename' => $filename,
                'data_length' => strlen($csvData),
            ]);

            $this->dispatch('download-csv',
                data: $csvData,
                filename: $filename
            );
        } catch (\Exception $e) {
            \Log::error('Error dispatching CSV download: '.$e->getMessage());
            throw $e;
        }
    }
}
