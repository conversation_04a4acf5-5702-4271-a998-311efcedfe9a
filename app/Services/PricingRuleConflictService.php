<?php

namespace App\Services;

use App\Enums\Payments\PaymentTypeEnum;
use App\Models\Area;
use App\Models\PricingRuleGender;
use App\Models\PricingRuleSeatNumber;
use App\Models\VehicleType;

class PricingRuleConflictService
{
    /**
     * Check for conflicts between global pricing rules and component-specific pricing adjustments
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    public function checkForConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $conflicts = [];
        $conflictChecks = [
            'areas' => $this->checkAreaConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'vehicle_types' => $this->checkVehicleTypeConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'seat_numbers' => $this->checkSeatNumberConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'gender_rules' => $this->checkGenderConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
        ];

        foreach ($conflictChecks as $key => $checkResults) {

            if (! empty($checkResults)) {
                $conflicts[$key] = $checkResults;
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in area pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkAreaConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $areas = Area::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($areas as $area) {
            $areaConflict = $this->checkComponentConflicts(
                $area,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($areaConflict)) {
                $conflicts[] = [
                    'id' => $area->id,
                    'name' => $area->name_en,
                    'conflicts' => $areaConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in vehicle type pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkVehicleTypeConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $vehicleTypes = VehicleType::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($vehicleTypes as $vehicleType) {
            $vehicleTypeConflict = $this->checkComponentConflicts(
                $vehicleType,
                $minBaseValue,
                $minDistanceValue,
                'additional_base_fare',
                'additional_price_per_km',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($vehicleTypeConflict)) {
                $conflicts[] = [
                    'id' => $vehicleType->id,
                    'name' => $vehicleType->name_en,
                    'conflicts' => $vehicleTypeConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in seat number pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkSeatNumberConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $seatNumbers = PricingRuleSeatNumber::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($seatNumbers as $seatNumber) {
            $seatNumberConflict = $this->checkComponentConflicts(
                $seatNumber,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($seatNumberConflict)) {
                $conflicts[] = [
                    'id' => $seatNumber->id,
                    'seats_number' => $seatNumber->seats_number,
                    'conflicts' => $seatNumberConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in gender pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkGenderConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $genderRules = PricingRuleGender::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($genderRules as $genderRule) {
            $genderConflict = $this->checkComponentConflicts(
                $genderRule,
                $minBaseValue,
                $minDistanceValue,
                'base_fare_fixed',
                'distance_fare_fixed',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($genderConflict)) {
                $conflicts[] = [
                    'id' => $genderRule->id,
                    'gender' => $genderRule->gender->value,
                    'conflicts' => $genderConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check component for base and distance fare conflicts
     *
     * @param  mixed  $component  The component to check
     * @param  float  $minBaseValue  Minimum allowed base value
     * @param  float  $minDistanceValue  Minimum allowed distance value
     * @param  string  $baseField  Field name for base fare
     * @param  string  $distanceField  Field name for distance fare
     * @param  string  $baseTypeField  Field name for base fare adjustment type
     * @param  string  $distanceTypeField  Field name for distance fare adjustment type
     * @return array Component conflicts
     */
    private function checkComponentConflicts(
        $component,
        float $minBaseValue,
        float $minDistanceValue,
        string $baseField,
        string $distanceField,
        string $baseTypeField,
        string $distanceTypeField
    ): array {
        $conflicts = [];

        if (
            $component->{$baseTypeField}?->value === 'fixed' &&
            (float) $component->{$baseField} < $minBaseValue
        ) {
            $conflicts['base_fare'] = [
                'current_value' => $component->{$baseField},
                'min_allowed' => $minBaseValue,
            ];
        }

        if (
            $component->{$distanceTypeField}?->value === 'fixed' &&
            (float) $component->{$distanceField} < $minDistanceValue
        ) {
            $conflicts['distance_fare'] = [
                'current_value' => $component->{$distanceField},
                'min_allowed' => $minDistanceValue,
            ];
        }

        return $conflicts;
    }

    /**
     * Calculate the minimum value based on the global price
     *
     * @param  float  $globalPrice  The global price
     * @return float The minimum value (-50% of global price)
     */
    private function calculateMinValue(float $globalPrice): float
    {
        $minValue = round(-($globalPrice / 2), 2);

        return $minValue;
    }
}
