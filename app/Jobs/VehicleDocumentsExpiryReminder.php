<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Vehicle;
use App\Notifications\DriverDocumentsExpiryNotification;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class VehicleDocumentsExpiryReminder implements ShouldQueue
{
    use Queueable,SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    public function handle(): void
    {
        Log::info('Starting VehicleDocumentsExpiryReminder job');

        $now = Carbon::now();
        Log::info('Current date/time', ['now' => $now->toDateTimeString()]);

        // Calculate target dates with startOfDay to ensure we only compare dates, not times
        $threeMonthsFromNow = $now->copy()->addMonths(3)->startOfDay();
        $twoWeeksFromNow = $now->copy()->addWeeks(2)->startOfDay();
        $today = $now->copy()->startOfDay();

        Log::info('Target expiry dates', [
            'three_months_from_now' => $threeMonthsFromNow->toDateString(),
            'two_weeks_from_now' => $twoWeeksFromNow->toDateString(),
            'today' => $today->toDateString(),
        ]);

        Vehicle::whereHas('documents')
            ->whereHas('drivers', function ($query) {
                $query->whereIn('global_status', ['active', 'blocked']);
            })
            ->with(['documents' => function ($query) {
                $query->select('vehicle_id', 'insurance_expiry', 'technical_inspection_expiry', 'roaming_permit_expiry');
            }, 'drivers.user'])
            ->chunkById(20, function ($vehicles) use ($today, $twoWeeksFromNow, $threeMonthsFromNow) {
                Log::info('Processing chunk of vehicles', ['chunk_size' => count($vehicles)]);

                foreach ($vehicles as $vehicle) {
                    Log::info('Checking vehicle documents', [
                        'vehicle_id' => $vehicle->id,
                        'license_plate' => $vehicle->license_plate_number,
                    ]);

                    if (! $vehicle->documents) {
                        Log::warning('Vehicle has no documents', ['vehicle_id' => $vehicle->id]);

                        continue;
                    }

                    $insurance_expiry = $vehicle->documents->insurance_expiry ?
                        Carbon::parse($vehicle->documents->insurance_expiry)->startOfDay() : null;
                    $technical_inspection_expiry = $vehicle->documents->technical_inspection_expiry ?
                        Carbon::parse($vehicle->documents->technical_inspection_expiry)->startOfDay() : null;
                    $roaming_permit_expiry = $vehicle->documents->roaming_permit_expiry ?
                        Carbon::parse($vehicle->documents->roaming_permit_expiry)->startOfDay() : null;

                    Log::info('Vehicle document expiry dates', [
                        'vehicle_id' => $vehicle->id,
                        'insurance_expiry' => $insurance_expiry ? $insurance_expiry->toDateString() : 'N/A',
                        'technical_inspection_expiry' => $technical_inspection_expiry ? $technical_inspection_expiry->toDateString() : 'N/A',
                        'roaming_permit_expiry' => $roaming_permit_expiry ? $roaming_permit_expiry->toDateString() : 'N/A',
                    ]);

                    $documents = [
                        'Insurance' => $insurance_expiry,
                        'Technical Inspection' => $technical_inspection_expiry,
                        'Roaming Permit' => $roaming_permit_expiry,
                    ];

                    $this->SendNotification($vehicle, $documents, $today, $twoWeeksFromNow, $threeMonthsFromNow);
                }
            });

        Log::info('Completed VehicleDocumentsExpiryReminder job');
    }

    public function SendNotification($vehicle, $documents, $today, $twoWeeksFromNow, $threeMonthsFromNow)
    {
        $recipient = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        Log::info('Found admin recipients', ['count' => count($recipient)]);

        $driver = $vehicle->drivers()->first();

        if (! $driver) {
            Log::warning('Vehicle has no assigned driver', ['vehicle_id' => $vehicle->id]);

            return;
        }

        if (! $driver->user) {
            Log::warning('Driver has no associated user', ['driver_id' => $driver->id]);

            return;
        }

        Log::info('Processing notifications for vehicle', [
            'vehicle_id' => $vehicle->id,
            'driver_id' => $driver->id,
            'driver_name' => $driver->user->name.' '.$driver->user->last_name,
        ]);

        foreach ($documents as $documentName => $documentExpiry) {
            if (! $documentExpiry) {
                Log::info('Document has no expiry date', [
                    'vehicle_id' => $vehicle->id,
                    'document_name' => $documentName,
                ]);

                continue;
            }

            // Check if document expires on one of our target dates
            $isToday = $documentExpiry->isSameDay($today);
            $isTwoWeeksFromNow = $documentExpiry->isSameDay($twoWeeksFromNow);
            $isThreeMonthsFromNow = $documentExpiry->isSameDay($threeMonthsFromNow);

            // For logging purposes
            $diffInDays = $today->diffInDays($documentExpiry, false);

            Log::info('Checking document expiry', [
                'document_name' => $documentName,
                'expiry_date' => $documentExpiry->toDateString(),
                'days_until_expiry' => $diffInDays,
                'is_today' => $isToday,
                'is_two_weeks' => $isTwoWeeksFromNow,
                'is_three_months' => $isThreeMonthsFromNow,
            ]);

            // Only send notifications for our three target dates
            if (! $isToday && ! $isTwoWeeksFromNow && ! $isThreeMonthsFromNow) {
                Log::info('Skipping notification - not a target date', [
                    'document_name' => $documentName,
                    'days_until_expiry' => $diffInDays,
                ]);

                continue;
            }

            if ($isToday) {
                $timeframe = 'today';
                $color = 'danger';
                $message = 'URGENT: The Driver '.$driver->user->name.' '.$driver->user->last_name.
                         ' vehicle with license plate '.$vehicle->license_plate_number.
                         ' has '.$documentName.' document expiring TODAY ('.$documentExpiry->toDateString().
                         '). Immediate action required.';
            } elseif ($isTwoWeeksFromNow) {
                $timeframe = '2 weeks';
                $color = 'danger';
                $message = 'The Driver '.$driver->user->name.' '.$driver->user->last_name.
                         ' vehicle with license plate '.$vehicle->license_plate_number.
                         ' has '.$documentName.' document expiring on '.$documentExpiry->toDateString().
                         ' (exactly '.$timeframe.' from now).';
            } else { // Three months
                $timeframe = '3 months';
                $color = 'warning';
                $message = 'The Driver '.$driver->user->name.' '.$driver->user->last_name.
                         ' vehicle with license plate '.$vehicle->license_plate_number.
                         ' has '.$documentName.' document expiring on '.$documentExpiry->toDateString().
                         ' (exactly '.$timeframe.' from now).';
            }

            Log::info('Sending notification', [
                'document_name' => $documentName,
                'expiry_date' => $documentExpiry->toDateString(),
                'timeframe' => $timeframe,
                'color' => $color,
            ]);

            foreach ($recipient as $user) {
                Log::info('Notifying admin', [
                    'admin_id' => $user->id,
                    'admin_name' => $user->name,
                ]);

                $user->notifyNow(new DriverDocumentsExpiryNotification(
                    'Driver vehicle '.$documentName.' document soon expires',
                    $message,
                    $color
                ));
            }
        }
    }
}
