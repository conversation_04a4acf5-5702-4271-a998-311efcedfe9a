<?php

namespace App\Jobs;

use App\Models\Driver;
use App\Models\User;
use App\Notifications\DriverDocumentsExpiryNotification;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DriverDocumentsExpiryReminder implements ShouldBeUnique, ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting DriverDocumentsExpiryReminder job');

        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        Log::info('Found admin users for notifications', ['count' => $admins->count()]);

        $now = Carbon::now();

        // Calculate target dates
        $threeMonthsFromNow = $now->copy()->addMonths(3)->startOfDay();
        $twoWeeksFromNow = $now->copy()->addWeeks(2)->startOfDay();
        $today = $now->copy()->startOfDay();

        Log::info('Checking for license expiry dates', [
            'three_months_date' => $threeMonthsFromNow->toDateString(),
            'two_weeks_date' => $twoWeeksFromNow->toDateString(),
            'today_date' => $today->toDateString(),
            'current_date' => $now->toDateString(),
        ]);

        // Get all active or blocked drivers with their documents
        $query = Driver::whereHas('documents')
            ->whereIn('global_status', ['active', 'blocked'])
            ->with(['documents', 'user']);

        $totalDrivers = $query->count();
        Log::info('Found drivers with documents', ['count' => $totalDrivers]);

        $query->chunkById(100, function ($drivers) use ($today, $twoWeeksFromNow, $threeMonthsFromNow, $admins, $totalDrivers) {
            Log::info('Processing chunk of drivers', ['chunk_size' => $drivers->count(), 'total' => $totalDrivers]);

            foreach ($drivers as $driver) {
                if (! $driver->documents || ! $driver->documents->license_expiry) {
                    Log::warning('Driver has no documents or license expiry date', [
                        'driver_id' => $driver->id,
                        'has_documents' => (bool) $driver->documents,
                    ]);

                    continue;
                }

                $licenseExpiry = Carbon::parse($driver->documents->license_expiry)->startOfDay();

                // Check if license expires on one of our target dates
                $isToday = $licenseExpiry->isSameDay($today);
                $isTwoWeeksFromNow = $licenseExpiry->isSameDay($twoWeeksFromNow);
                $isThreeMonthsFromNow = $licenseExpiry->isSameDay($threeMonthsFromNow);

                // For logging purposes
                $daysUntilExpiry = $today->diffInDays($licenseExpiry, false);

                Log::info('Checking driver license', [
                    'driver_id' => $driver->id,
                    'driver_name' => $driver->user->name.' '.$driver->user->last_name,
                    'license_expiry' => $licenseExpiry->toDateString(),
                    'days_until_expiry' => $daysUntilExpiry,
                    'is_today' => $isToday,
                    'is_two_weeks' => $isTwoWeeksFromNow,
                    'is_three_months' => $isThreeMonthsFromNow,
                ]);

                // Only send notifications for our three target dates
                if (! $isToday && ! $isTwoWeeksFromNow && ! $isThreeMonthsFromNow) {
                    Log::info('Skipping notification - not a target date', [
                        'driver_id' => $driver->id,
                        'days_until_expiry' => $daysUntilExpiry,
                    ]);

                    continue;
                }

                if ($isToday) {
                    $timeframe = 'today';
                    $color = 'danger';
                    $message = "URGENT: The driver {$driver->user->name} {$driver->user->last_name} ".
                              "has their license document expiring TODAY ({$licenseExpiry->toDateString()}). ".
                              'Immediate action required.';
                } elseif ($isTwoWeeksFromNow) {
                    $timeframe = '2 weeks';
                    $color = 'danger';
                    $message = "The driver {$driver->user->name} {$driver->user->last_name} ".
                              "has their license document expiring on {$licenseExpiry->toDateString()} ".
                              "(exactly {$timeframe} from now).";
                } else { // Three months
                    $timeframe = '3 months';
                    $color = 'warning';
                    $message = "The driver {$driver->user->name} {$driver->user->last_name} ".
                              "has their license document expiring on {$licenseExpiry->toDateString()} ".
                              "(exactly {$timeframe} from now).";
                }

                Log::info('Sending expiry notification', [
                    'driver_id' => $driver->id,
                    'days_until_expiry' => $daysUntilExpiry,
                    'timeframe' => $timeframe,
                    'color' => $color,
                ]);

                foreach ($admins as $admin) {
                    Log::info('Notifying admin', [
                        'admin_id' => $admin->id,
                        'admin_name' => $admin->name,
                    ]);

                    $admin->notifyNow(new DriverDocumentsExpiryNotification(
                        'Driver license soon expires',
                        $message,
                        $color
                    ));
                }
            }
        });

        Log::info('Completed DriverDocumentsExpiryReminder job');
    }
}
