<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class TripCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request)
    {
        $data = [];

        if ($this->collection->isNotEmpty() && $this->collection[0]->status->value == 'completed') {
            $data = $this->collection->map(function ($item) {
                return [
                    'id' => $item->id,
                    'status' => $item->status->value,
                    'actual_departure_time' => $item->actual_departure_time ?? null,
                    'pricing_breakdown' => json_decode($item->pricing_breakdown, true),
                    'departureAddress' => [
                        'Address' => $item->TripLocation->departure_address,
                        // 'full_address' => $item->departureAddress->full_address,
                        // 'postal_address' => json_decode($item->departureAddress->postal_address, true),
                        'label' => $item->departure_label ?? null,
                    ],
                    'arrivalAddress' => [
                        'Address' => $item->TripLocation->arrival_address,
                        // 'full_address' => $item->arrivalAddress->full_address,
                        // 'postal_address' => json_decode($item->arrivalAddress->postal_address, true),
                        'label' => $item->arrival_label ?? null,
                    ],
                ];
            });
        } else {
            $data = $this->collection->map(function ($item) {
                return [
                    'id' => $item->id,
                    'status' => $item->status->value,
                    'actual_departure_time' => $item->actual_departure_time ?? null,
                    'estimated_departure_time' => $item->estimated_departure_time ?? null,
                    'pricing_breakdown' => json_decode($item->pricing_breakdown, true),
                    'departureAddress' => [
                        'Address' => $item->TripLocation->departure_address,
                        // 'full_address' => $item->departureAddress->full_address,
                        // 'postal_address' => json_decode($item->departureAddress->postal_address, true),
                        'label' => $item->departure_label ?? null,
                    ],
                    'arrivalAddress' => [
                        'Address' => $item->TripLocation->arrival_address,
                        // 'full_address' => $item->arrivalAddress->full_address,
                        // 'postal_address' => json_decode($item->arrivalAddress->postal_address, true),
                        'label' => $item->arrival_label ?? null,
                    ],
                ];
            });
        }

        return [
            'rides' => $data,
            'meta' => [
                'current_page' => $this->currentPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'next_page_url' => $this->nextPageUrl(),
                'prev_page_url' => $this->previousPageUrl(),
            ],
        ];
    }
}
