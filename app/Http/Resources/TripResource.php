<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TripResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        if ($this->driver_id != null) {
            $driver_coverPicture = $this->driver->user->cover_picture ? env('APP_URL', '/').'/storage/'.$this->driver->user->cover_picture : null;
        }

        // Generate polyline on-the-fly
        // it misses the trip duration
        return [
            'id' => $this->id,
            'status' => $this->status ?? null,
            'distance' => $this->distance ?? null,
            'polyline' => $this->tripLocation->polyline ?? null,
            'notes' => $this->rider_notes ?? null,
            'estimated_departure_time' => $this->estimated_departure_time ? $this->estimated_departure_time->toIso8601String() : null,
            'actual_departure_time' => $this->actual_departure_time ? $this->actual_departure_time->toIso8601String() : null,
            'estimated_arrival_time' => $this->estimated_arrival_time ? $this->estimated_arrival_time->toIso8601String() : null,
            'actual_arrival_time' => $this->actual_arrival_time ? $this->actual_arrival_time->toIso8601String() : null,
            'date' => $this->created_at->format('Y-m-d'),
            'total_price' => $this->total_price ?? null,
            'duration' => $this->tripDuration() ? $this->tripDuration()->format('%i') : null,
            'departureAddress' => [
                'departure_address' => $this->tripLocation?->departure_address ?? null,
                'address_label' => $this->departure_label ?? null,
                'latitude' => $this->tripLocation?->departure_lat ?? null,
                'longitude' => $this->tripLocation?->departure_lng ?? null,
            ],
            'arrivalAddress' => [
                'arrival_address' => $this->tripLocation?->arrival_address ?? null,
                'address_label' => $this->arrival_label ?? null,
                'latitude' => $this->tripLocation?->arrival_lat ?? null,
                'longitude' => $this->tripLocation?->arrival_lng ?? null,
            ],
            'driver' => [
                'id' => $this->driver?->id ?? null,
                'name' => $this->driver?->user?->name ?? null,
                'gender' => $this->driver?->user?->gender ?? null,
                'driver_image' => $this->driver?->user?->cover_picture
                    ? env('APP_URL', '/').'/storage/'.$this->driver->user->cover_picture
                    : env('APP_URL', '/').'/images/avatar.png',
                'driver_rating' => $this->rating ?? null,
            ],
            'rider' => [
                'id' => $this->rider?->id ?? null,
                'name' => $this->rider?->user?->name ?? null,
                'last_name' => $this->rider?->user?->last_name ?? null,
                'full_name' => $this->rider?->user?->full_name ?? null,
                'gender' => $this->rider?->user?->gender ?? null,
                'rider_image' => $this->rider?->user?->cover_picture
                    ? env('APP_URL', '/').'/storage/'.$this->rider->user->cover_picture
                    : env('APP_URL', '/').'/images/avatar.png',
                // 'driver_image' => $driver_coverPicture ?? null,
                // 'rider' => $this->driver?->tripRatings[0]?->trip_rating ?? null,
            ],
            'vehicle' => [
                'id' => $this->vehicle?->id ?? null,
                'vehicle_brand' => $this->vehicle?->vehicleModel?->vehicleBrand?->id ?? null,
                'vehicle_model' => $this->vehicle?->vehicleModel?->name_en ?? null,
                'vehicle_type' => $this->vehicle?->vehicleType?->name_en ?? null,
                'license_plate_number' => $this->vehicle?->license_plate_number ?? null,
                'vehicle_image' => $this->vehicle?->image
                    ? env('APP_URL', '/').'/storage/'.$this->vehicle?->image
                    : env('APP_URL', '/').'/images/vehicle.jpg',
                'vehicle_rating' => $this->tripRatings()->exists() ? $this->tripRatings->first()->rider_to_car_rating : null,
            ],
        ];
    }
}
