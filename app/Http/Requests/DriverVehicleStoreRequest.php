<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class DriverVehicleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'vehicle_photo' => 'required|image|mimes:jpeg,png,jpg|max:5120',
            'vehicle_type' => ['required', Rule::in(['passenger', 'freight'])],
            'license_plate_number' => ['required', 'string', 'regex:/^(?!.*--)[\d-]{1,30}$/'],
            'share_benefits' => ['sometimes'],
            'seats_number' => [
                'required_if:vehicle_type,passenger',
                'integer', Rule::in([2, 4, 6]),
            ],
            'vehicle_model' => ['required', 'integer', 'exists:vehicle_models,id'],
            'year' => ['required', 'regex:/^\d{4}$/'],
            'vehicle_color' => ['required', 'string'],
            'vehicle_equipments' => ['sometimes', 'string', function ($attribute, $value, $fail) {
                $vehicleEquipments = json_decode($value, true);

                if (! is_array($vehicleEquipments)) {
                    return $fail('The vehicle_equipments format is invalid.');
                }

                foreach ($vehicleEquipments as $equipmentId) {
                    if (! is_numeric($equipmentId)) {
                        return $fail('Each vehicle equipment must be numeric.');
                    }
                    $exists = DB::table('vehicle_equipment')->where('id', $equipmentId)->exists();
                    if (! $exists) {
                        return $fail("The vehicle equipment with ID $equipmentId does not exist.");
                    }
                }
            }],
            'insurance' => ['required', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'technical_inspection' => ['required',  'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'roaming_permit' => ['required',  'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'insurance_expiry' => ['required', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'technical_inspection_expiry' => ['required', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'roaming_permit_expiry' => ['required', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'is_covered' => ['required_if:vehicle_type,freight', 'string'],
            'weight_category' => ['required_if:vehicle_type,freight', Rule::in(['under_1000', 'over_1000'])],
        ];
    }
}
