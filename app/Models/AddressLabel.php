<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AddressLabel extends Model
{
    /** @use HasFactory<\Database\Factories\AddressLabelFactory> */
    use HasFactory;

    protected $fillable = ['label', 'icon', 'address_id'];

    protected $table = 'address_label';

    public function address()
    {
        return $this->belongsTo(Address::class, 'address_id');
    }
}
