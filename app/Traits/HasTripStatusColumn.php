<?php

namespace App\Traits;

use App\Enums\Trips\TripStatus;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\TextColumn;

trait HasTripStatusColumn
{
    public static function getTripStatusColumn(string $name = 'status', string $label = 'Status'): TextColumn
    {
        return self::configureTripStatusComponent(
            TextColumn::make($name),
            $label
        );
    }

    public static function getTripStatusEntry(string $name = 'status', string $label = 'Status'): TextEntry
    {
        return self::configureTripStatusComponent(
            TextEntry::make($name),
            $label
        );
    }

    private static function configureTripStatusComponent($component, string $label)
    {
        return $component
            ->label($label)
            ->badge()
            ->icon(self::getTripStatusIcon())
            ->color(self::getTripStatusColor())
            ->getStateUsing(self::getTripStatusState());
    }

    private static function getTripStatusIcon(): \Closure
    {
        return function ($record) {
            if (! $record->status) {
                return null;
            }

            // Special handling for no_show cancellations - always use no_show icon
            if (($record->status === TripStatus::canceled || $record->status === TripStatus::no_show)
                && $record->cancellation && $record->cancellation->reason === 'no_show') {
                return TripStatus::no_show->getIcon();
            }

            return $record->status->getIcon();
        };
    }

    private static function getTripStatusColor(): \Closure
    {
        return function ($record) {
            if (! $record->status) {
                return null;
            }

            // Special handling for no_show cancellations - always use no_show color
            if (($record->status === TripStatus::canceled || $record->status === TripStatus::no_show)
                && $record->cancellation && $record->cancellation->reason === 'no_show') {
                return TripStatus::no_show->getColor();
            }

            return $record->status->getColor();
        };
    }

    private static function getTripStatusState(): \Closure
    {
        return function ($record) {
            if (! $record->status) {
                return 'No Status Provided';
            }

            // Special handling for no_show cancellations
            if (($record->status === TripStatus::canceled || $record->status === TripStatus::no_show)
                && $record->cancellation && $record->cancellation->reason === 'no_show') {
                return 'No Show';
            }

            if ($record->status === TripStatus::canceled) {
                // Otherwise show who canceled the trip
                if ($record->cancelled_by) {
                    return $record->cancelled_by === 'driver'
                        ? 'Canceled by Driver'
                        : 'Canceled by Rider';
                }

                return 'Canceled';
            }

            return $record->status->getLabel();
        };
    }
}
